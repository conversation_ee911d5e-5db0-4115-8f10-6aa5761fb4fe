# TextEditor Component

A comprehensive rich text editor component built with TipTap for Vue.js applications. This component provides a full-featured WYSIWYG editor with an intuitive toolbar and extensive formatting capabilities.

## Features

### Text Formatting
- **Bold**, *Italic*, <u>Underline</u>, ~~Strikethrough~~
- Subscript and Superscript
- Font family selection
- Text color and highlight color
- Text alignment (left, center, right, justify)

### Block Elements
- Headings (H1-H6)
- Paragraphs
- Blockquotes
- Code blocks with syntax highlighting
- Hard breaks

### Lists
- Bullet lists (unordered)
- Numbered lists (ordered)
- Proper list nesting support

### Advanced Features
- Tables with insert/delete rows/columns
- Image insertion (URL-based)
- Link creation and editing
- Undo/Redo functionality
- Responsive toolbar design

### Integration Features
- Vue 3 Composition API
- v-model support
- Form validation integration
- Error state handling
- Disabled state support
- TypeScript support
- Tailwind CSS styling

## Installation

The component uses TipTap extensions that are already installed in the project:

```bash
npm install @tiptap/vue-3 @tiptap/starter-kit @tiptap/extension-text-style @tiptap/extension-font-family @tiptap/extension-color @tiptap/extension-highlight @tiptap/extension-text-align @tiptap/extension-table @tiptap/extension-table-row @tiptap/extension-table-cell @tiptap/extension-table-header @tiptap/extension-image @tiptap/extension-link @tiptap/extension-underline @tiptap/extension-subscript @tiptap/extension-superscript
```

## Basic Usage

```vue
<script setup lang="ts">
import TextEditor from '@/components/common/shared/TextEditor.vue';
import { ref } from 'vue';

const content = ref('<p>Hello, World!</p>');
</script>

<template>
  <TextEditor v-model="content" label="Content" />
</template>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `modelValue` | `string` | `''` | The HTML content of the editor |
| `label` | `string` | `undefined` | Label text displayed above the editor |
| `placeholder` | `string` | `'Start typing...'` | Placeholder text when editor is empty |
| `error` | `string` | `undefined` | Error message to display |
| `disabled` | `boolean` | `false` | Whether the editor is disabled |
| `required` | `boolean` | `false` | Whether the field is required (shows asterisk) |
| `id` | `string` | `auto-generated` | Unique ID for the editor |
| `minHeight` | `string` | `'200px'` | Minimum height of the editor content area |

## Events

| Event | Payload | Description |
|-------|---------|-------------|
| `update:modelValue` | `string` | Emitted when the content changes |

## Form Integration

### With Inertia Forms

```vue
<script setup lang="ts">
import { useForm } from '@inertiajs/vue3';
import TextEditor from '@/components/common/shared/TextEditor.vue';

const form = useForm({
  title: '',
  content: '',
});

const submit = () => {
  form.post('/articles');
};
</script>

<template>
  <form @submit.prevent="submit">
    <TextEditor
      v-model="form.content"
      :error="form.errors.content"
      label="Article Content"
      placeholder="Write your article..."
      required
    />
    <button type="submit">Submit</button>
  </form>
</template>
```

### With Validation Composable

```vue
<script setup lang="ts">
import { useValidation } from '@/composables/useValidation';
import TextEditor from '@/components/common/shared/TextEditor.vue';

const { form, validateForm } = useValidation({
  content: '',
});

const validationRules = {
  content: ['required', 'min:10'],
};

const submit = () => {
  if (validateForm(validationRules)) {
    // Submit form
  }
};
</script>

<template>
  <form @submit.prevent="submit">
    <TextEditor
      v-model="form.content"
      :error="form.errors.content"
      label="Content"
      required
    />
  </form>
</template>
```

## Replacing VTextarea

The TextEditor component is designed as a drop-in replacement for VTextarea:

```vue
<!-- Before -->
<VTextarea
  v-model="form.description"
  :error="form.errors.description"
  label="Description"
  required
/>

<!-- After -->
<TextEditor
  v-model="form.description"
  :error="form.errors.description"
  label="Description"
  required
/>
```

## Styling

The component uses Tailwind CSS classes and includes comprehensive styling for:

- Toolbar with grouped controls
- Dropdown menus for font selection and headings
- Color pickers for text and highlight colors
- Responsive design for mobile devices
- Error and disabled states
- Focus states and hover effects

### Custom Styling

You can customize the appearance by overriding CSS classes:

```css
.text-editor {
  /* Custom editor container styles */
}

.text-editor-toolbar {
  /* Custom toolbar styles */
}

.text-editor-button {
  /* Custom button styles */
}
```

## Toolbar Features

### Undo/Redo
- Undo (Ctrl+Z)
- Redo (Ctrl+Y)

### Font and Headings
- Font family dropdown
- Heading level selection (H1-H6, Paragraph)

### Text Formatting
- Bold (Ctrl+B)
- Italic (Ctrl+I)
- Underline (Ctrl+U)
- Strikethrough
- Subscript
- Superscript

### Colors
- Text color picker
- Highlight color picker

### Alignment
- Align left
- Align center
- Align right
- Justify

### Lists
- Bullet list
- Numbered list

### Block Elements
- Blockquote
- Code block

### Insert
- Table insertion
- Image insertion (URL)
- Link creation

## Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| `Ctrl+B` | Bold |
| `Ctrl+I` | Italic |
| `Ctrl+U` | Underline |
| `Ctrl+Z` | Undo |
| `Ctrl+Y` | Redo |
| `Ctrl+Shift+Z` | Redo |

## Browser Support

The component supports all modern browsers:
- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

## Demo Pages

- `/demo/text-editor` - Basic component demo
- `/demo/text-editor-form` - Form integration demo

## Troubleshooting

### Content Not Updating
Ensure you're using `v-model` correctly and the parent component is reactive.

### Styling Issues
Make sure Tailwind CSS is properly configured and the component styles are imported.

### Performance Issues
For large documents, consider implementing lazy loading or pagination.

## Contributing

When contributing to the TextEditor component:

1. Follow the existing code style
2. Add tests for new features
3. Update documentation
4. Test across different browsers
5. Ensure accessibility compliance

## License

This component is part of the project and follows the same license terms.
