/* TipTap Text Editor Styles */
.text-editor {
  @apply border border-gray-200 rounded-lg bg-white;
}

.text-editor.error {
  @apply border-red-300;
}

.text-editor.disabled {
  @apply bg-gray-50 opacity-60;
}

/* Toolbar Styles */
.text-editor-toolbar {
  @apply flex flex-wrap items-center gap-1 p-3 border-b border-gray-200 bg-gray-50;
}

.text-editor-toolbar-group {
  @apply flex items-center gap-1 pr-3 border-r border-gray-300 last:border-r-0 last:pr-0;
}

.text-editor-button {
  @apply flex items-center justify-center w-8 h-8 rounded border border-transparent bg-transparent text-gray-600 hover:bg-gray-100 hover:text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 transition-colors;
}

.text-editor-button.active {
  @apply bg-blue-100 text-blue-700 border-blue-200;
}

.text-editor-button:disabled {
  @apply opacity-50 cursor-not-allowed hover:bg-transparent hover:text-gray-600;
}

.text-editor-button svg {
  @apply w-4 h-4;
}

/* Dropdown Styles */
.text-editor-dropdown {
  @apply relative;
}

.text-editor-dropdown-button {
  @apply flex items-center gap-1 px-3 py-1.5 text-sm border border-gray-300 rounded bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1;
}

.text-editor-dropdown-menu {
  @apply absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto;
}

.text-editor-dropdown-item {
  @apply block w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100;
}

.text-editor-dropdown-item.active {
  @apply bg-blue-50 text-blue-700;
}

/* Color Picker Styles */
.text-editor-color-picker {
  @apply grid grid-cols-8 gap-1 p-2;
}

.text-editor-color-button {
  @apply w-6 h-6 rounded border border-gray-300 cursor-pointer hover:scale-110 transition-transform;
}

.text-editor-color-button.active {
  @apply ring-2 ring-blue-500 ring-offset-1;
}

/* Editor Content Styles */
.text-editor-content {
  @apply p-4 min-h-[200px] focus:outline-none;
}

.text-editor-content.disabled {
  @apply bg-gray-50;
}

/* ProseMirror Content Styles */
.ProseMirror {
  @apply outline-none;
}

.ProseMirror h1 {
  @apply text-3xl font-bold mb-4 mt-6 first:mt-0;
}

.ProseMirror h2 {
  @apply text-2xl font-bold mb-3 mt-5 first:mt-0;
}

.ProseMirror h3 {
  @apply text-xl font-bold mb-3 mt-4 first:mt-0;
}

.ProseMirror h4 {
  @apply text-lg font-bold mb-2 mt-3 first:mt-0;
}

.ProseMirror h5 {
  @apply text-base font-bold mb-2 mt-3 first:mt-0;
}

.ProseMirror h6 {
  @apply text-sm font-bold mb-2 mt-2 first:mt-0;
}

.ProseMirror p {
  @apply mb-3 last:mb-0;
}

.ProseMirror ul {
  @apply list-disc list-inside mb-3 pl-4;
}

.ProseMirror ol {
  @apply list-decimal list-inside mb-3 pl-4;
}

.ProseMirror li {
  @apply mb-1;
}

.ProseMirror blockquote {
  @apply border-l-4 border-gray-300 pl-4 italic text-gray-600 mb-3;
}

.ProseMirror code {
  @apply bg-gray-100 px-1 py-0.5 rounded text-sm font-mono;
}

.ProseMirror pre {
  @apply bg-gray-100 p-3 rounded mb-3 overflow-x-auto;
}

.ProseMirror pre code {
  @apply bg-transparent p-0;
}

.ProseMirror a {
  @apply text-blue-600 underline hover:text-blue-800;
}

.ProseMirror img {
  @apply max-w-full h-auto rounded;
}

.ProseMirror strong {
  @apply font-bold;
}

.ProseMirror em {
  @apply italic;
}

.ProseMirror u {
  @apply underline;
}

.ProseMirror s {
  @apply line-through;
}

/* Table Styles */
.ProseMirror table {
  @apply border-collapse border border-gray-300 mb-3 w-full;
}

.ProseMirror th {
  @apply border border-gray-300 px-3 py-2 bg-gray-100 font-semibold text-left;
}

.ProseMirror td {
  @apply border border-gray-300 px-3 py-2;
}

.ProseMirror .selectedCell {
  @apply bg-blue-50;
}

/* Placeholder Styles */
.ProseMirror p.is-editor-empty:first-child::before {
  @apply text-gray-400 float-left h-0 pointer-events-none;
  content: attr(data-placeholder);
}

/* Focus Styles */
.text-editor:focus-within {
  @apply ring-2 ring-blue-500 ring-offset-1;
}

/* Text Alignment */
.ProseMirror .text-left {
  @apply text-left;
}

.ProseMirror .text-center {
  @apply text-center;
}

.ProseMirror .text-right {
  @apply text-right;
}

.ProseMirror .text-justify {
  @apply text-justify;
}

/* Responsive Toolbar */
@media (max-width: 768px) {
  .text-editor-toolbar {
    @apply flex-col items-start gap-2;
  }
  
  .text-editor-toolbar-group {
    @apply border-r-0 border-b border-gray-300 pb-2 last:border-b-0 last:pb-0;
  }
}
