<script setup lang="ts">
import { ref, watch, onBeforeUnmount } from 'vue';
import { Editor, EditorContent } from '@tiptap/vue-3';
import StarterKit from '@tiptap/starter-kit';
import TextStyle from '@tiptap/extension-text-style';
import FontFamily from '@tiptap/extension-font-family';
import Color from '@tiptap/extension-color';
import Highlight from '@tiptap/extension-highlight';
import TextAlign from '@tiptap/extension-text-align';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import Underline from '@tiptap/extension-underline';
import Subscript from '@tiptap/extension-subscript';
import Superscript from '@tiptap/extension-superscript';
import { v4 as uuid } from 'uuid';

interface Props {
  modelValue?: string;
  label?: string;
  placeholder?: string;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  id?: string;
  minHeight?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: 'Start typing...',
  disabled: false,
  required: false,
  id: () => `text-editor-${uuid()}`,
  minHeight: '200px',
});

const emit = defineEmits<{
  'update:modelValue': [value: string];
}>();

// Editor instance
const editor = ref<Editor | null>(null);

// Dropdown states
const showFontFamilyDropdown = ref(false);
const showHeadingDropdown = ref(false);
const showTextColorDropdown = ref(false);
const showHighlightColorDropdown = ref(false);

// Font families
const fontFamilies = [
  { name: 'Default', value: '' },
  { name: 'Arial', value: 'Arial, sans-serif' },
  { name: 'Helvetica', value: 'Helvetica, sans-serif' },
  { name: 'Times New Roman', value: 'Times New Roman, serif' },
  { name: 'Georgia', value: 'Georgia, serif' },
  { name: 'Courier New', value: 'Courier New, monospace' },
  { name: 'Verdana', value: 'Verdana, sans-serif' },
];

// Heading options
const headingOptions = [
  { name: 'Paragraph', value: 'paragraph' },
  { name: 'Heading 1', value: 1 },
  { name: 'Heading 2', value: 2 },
  { name: 'Heading 3', value: 3 },
  { name: 'Heading 4', value: 4 },
  { name: 'Heading 5', value: 5 },
  { name: 'Heading 6', value: 6 },
];

// Color palette
const colors = [
  '#000000', '#434343', '#666666', '#999999', '#b7b7b7', '#cccccc', '#d9d9d9', '#efefef',
  '#f3f3f3', '#ffffff', '#980000', '#ff0000', '#ff9900', '#ffff00', '#00ff00', '#00ffff',
  '#4a86e8', '#0000ff', '#9900ff', '#ff00ff', '#e6b8af', '#f4cccc', '#fce5cd', '#fff2cc',
  '#d9ead3', '#d0e0e3', '#c9daf8', '#cfe2f3', '#d9d2e9', '#ead1dc',
];

// Initialize editor
const initEditor = () => {
  editor.value = new Editor({
    content: props.modelValue,
    editable: !props.disabled,
    extensions: [
      StarterKit,
      TextStyle,
      FontFamily,
      Color,
      Highlight.configure({ multicolor: true }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
      Image,
      Link.configure({
        openOnClick: false,
      }),
      Underline,
      Subscript,
      Superscript,
    ],
    onUpdate: ({ editor }) => {
      emit('update:modelValue', editor.getHTML());
    },
  });
};

// Watch for prop changes
watch(() => props.modelValue, (newValue) => {
  if (editor.value && editor.value.getHTML() !== newValue) {
    editor.value.commands.setContent(newValue);
  }
});

watch(() => props.disabled, (newValue) => {
  if (editor.value) {
    editor.value.setEditable(!newValue);
  }
});

// Toolbar actions
const toggleBold = () => editor.value?.chain().focus().toggleBold().run();
const toggleItalic = () => editor.value?.chain().focus().toggleItalic().run();
const toggleUnderline = () => editor.value?.chain().focus().toggleUnderline().run();
const toggleStrike = () => editor.value?.chain().focus().toggleStrike().run();
const toggleSubscript = () => editor.value?.chain().focus().toggleSubscript().run();
const toggleSuperscript = () => editor.value?.chain().focus().toggleSuperscript().run();

const setFontFamily = (fontFamily: string) => {
  if (fontFamily) {
    editor.value?.chain().focus().setFontFamily(fontFamily).run();
  } else {
    editor.value?.chain().focus().unsetFontFamily().run();
  }
  showFontFamilyDropdown.value = false;
};

const setHeading = (level: number | string) => {
  if (level === 'paragraph') {
    editor.value?.chain().focus().setParagraph().run();
  } else {
    editor.value?.chain().focus().toggleHeading({ level: level as 1 | 2 | 3 | 4 | 5 | 6 }).run();
  }
  showHeadingDropdown.value = false;
};

const setTextColor = (color: string) => {
  editor.value?.chain().focus().setColor(color).run();
  showTextColorDropdown.value = false;
};

const setHighlightColor = (color: string) => {
  editor.value?.chain().focus().setHighlight({ color }).run();
  showHighlightColorDropdown.value = false;
};

const setTextAlign = (alignment: 'left' | 'center' | 'right' | 'justify') => {
  editor.value?.chain().focus().setTextAlign(alignment).run();
};

const toggleBulletList = () => editor.value?.chain().focus().toggleBulletList().run();
const toggleOrderedList = () => editor.value?.chain().focus().toggleOrderedList().run();
const toggleBlockquote = () => editor.value?.chain().focus().toggleBlockquote().run();
const toggleCodeBlock = () => editor.value?.chain().focus().toggleCodeBlock().run();

const insertTable = () => {
  editor.value?.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
};

const addImage = () => {
  const url = window.prompt('Enter image URL:');
  if (url) {
    editor.value?.chain().focus().setImage({ src: url }).run();
  }
};

const addLink = () => {
  const url = window.prompt('Enter URL:');
  if (url) {
    editor.value?.chain().focus().setLink({ href: url }).run();
  }
};

const undo = () => editor.value?.chain().focus().undo().run();
const redo = () => editor.value?.chain().focus().redo().run();

// Helper functions
const isActive = (name: string, attrs?: any) => {
  return editor.value?.isActive(name, attrs) || false;
};

const canUndo = () => editor.value?.can().undo() || false;
const canRedo = () => editor.value?.can().redo() || false;

// Lifecycle
initEditor();

onBeforeUnmount(() => {
  editor.value?.destroy();
});
</script>

<template>
  <div class="space-y-6" :class="$attrs.class">
    <label v-if="label" class="mb-1.5 block text-sm font-medium text-gray-700" :for="id">
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>
    
    <div 
      class="text-editor"
      :class="[
        error ? 'error' : '',
        disabled ? 'disabled' : ''
      ]"
    >
      <!-- Toolbar -->
      <div class="text-editor-toolbar" v-if="!disabled">
        <!-- Undo/Redo Group -->
        <div class="text-editor-toolbar-group">
          <button
            type="button"
            class="text-editor-button"
            :disabled="!canUndo()"
            @click="undo"
            title="Undo"
          >
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"/>
            </svg>
          </button>
          <button
            type="button"
            class="text-editor-button"
            :disabled="!canRedo()"
            @click="redo"
            title="Redo"
          >
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 10H11a8 8 0 00-8 8v2m18-10l-6-6m6 6l-6 6"/>
            </svg>
          </button>
        </div>

        <!-- Font Family Group -->
        <div class="text-editor-toolbar-group">
          <div class="text-editor-dropdown">
            <button
              type="button"
              class="text-editor-dropdown-button"
              @click="showFontFamilyDropdown = !showFontFamilyDropdown"
            >
              Font
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
              </svg>
            </button>
            <div v-if="showFontFamilyDropdown" class="text-editor-dropdown-menu">
              <button
                v-for="font in fontFamilies"
                :key="font.value"
                type="button"
                class="text-editor-dropdown-item"
                :class="{ active: isActive('textStyle', { fontFamily: font.value }) }"
                @click="setFontFamily(font.value)"
                :style="{ fontFamily: font.value }"
              >
                {{ font.name }}
              </button>
            </div>
          </div>
        </div>

        <!-- Heading Group -->
        <div class="text-editor-toolbar-group">
          <div class="text-editor-dropdown">
            <button
              type="button"
              class="text-editor-dropdown-button"
              @click="showHeadingDropdown = !showHeadingDropdown"
            >
              Heading
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
              </svg>
            </button>
            <div v-if="showHeadingDropdown" class="text-editor-dropdown-menu">
              <button
                v-for="heading in headingOptions"
                :key="heading.value"
                type="button"
                class="text-editor-dropdown-item"
                :class="{ 
                  active: heading.value === 'paragraph' 
                    ? isActive('paragraph') 
                    : isActive('heading', { level: heading.value }) 
                }"
                @click="setHeading(heading.value)"
              >
                {{ heading.name }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Editor Content -->
      <div 
        class="text-editor-content"
        :class="{ disabled }"
        :style="{ minHeight }"
      >
        <EditorContent :editor="editor" />
      </div>
    </div>

    <p v-if="error" class="text-theme-sm text-error-500">{{ error }}</p>
  </div>
</template>

<style>
@import '../../../css/components/text-editor.css';
</style>
