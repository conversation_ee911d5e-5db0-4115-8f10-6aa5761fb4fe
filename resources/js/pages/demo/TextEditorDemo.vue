<script setup lang="ts">
import { ref } from 'vue';
import { Head } from '@inertiajs/vue3';
import TextEditor from '@/components/common/shared/TextEditor.vue';
import Button from '@/components/common/shared/Button.vue';

const content = ref('<p>Welcome to the <strong>TipTap Text Editor</strong>! This is a comprehensive rich text editor with full formatting capabilities.</p>');
const content2 = ref('');
const hasError = ref(false);
const isDisabled = ref(false);

const toggleError = () => {
  hasError.value = !hasError.value;
};

const toggleDisabled = () => {
  isDisabled.value = !isDisabled.value;
};

const clearContent = () => {
  content.value = '';
  content2.value = '';
};

const setSampleContent = () => {
  content.value = `
    <h1>TipTap Rich Text Editor Demo</h1>
    <h2>Text Formatting Features</h2>
    <p>This editor supports <strong>bold text</strong>, <em>italic text</em>, <u>underlined text</u>, and <s>strikethrough text</s>.</p>
    
    <h3>Lists and Structure</h3>
    <ul>
      <li>Bullet point one</li>
      <li>Bullet point two with <strong>bold text</strong></li>
      <li>Bullet point three</li>
    </ul>
    
    <ol>
      <li>Numbered list item one</li>
      <li>Numbered list item two</li>
      <li>Numbered list item three</li>
    </ol>
    
    <blockquote>
      This is a blockquote to demonstrate the quote formatting feature.
    </blockquote>
    
    <h3>Code and Links</h3>
    <p>You can add <code>inline code</code> and create <a href="https://example.com">links</a> easily.</p>
    
    <pre><code>// This is a code block
function hello() {
  console.log("Hello, World!");
}</code></pre>
    
    <h3>Tables</h3>
    <table>
      <thead>
        <tr>
          <th>Feature</th>
          <th>Status</th>
          <th>Notes</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>Bold/Italic</td>
          <td>✅ Supported</td>
          <td>Basic formatting</td>
        </tr>
        <tr>
          <td>Tables</td>
          <td>✅ Supported</td>
          <td>With headers</td>
        </tr>
        <tr>
          <td>Images</td>
          <td>✅ Supported</td>
          <td>URL-based</td>
        </tr>
      </tbody>
    </table>
    
    <h3>Text Alignment</h3>
    <p style="text-align: left;">This text is left-aligned (default).</p>
    <p style="text-align: center;">This text is center-aligned.</p>
    <p style="text-align: right;">This text is right-aligned.</p>
    <p style="text-align: justify;">This text is justified. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
  `;
};
</script>

<template>
  <Head title="TextEditor Demo" />
  
  <div class="p-8 max-w-6xl mx-auto">
    <h1 class="text-3xl font-bold mb-8">TipTap Text Editor Demo</h1>
    
    <!-- Control buttons -->
    <div class="mb-6 space-x-4">
      <Button @click="toggleError" variant="outline">
        {{ hasError ? 'Remove Error' : 'Show Error' }}
      </Button>
      <Button @click="toggleDisabled" variant="outline">
        {{ isDisabled ? 'Enable Editor' : 'Disable Editor' }}
      </Button>
      <Button @click="clearContent" variant="outline">
        Clear All Content
      </Button>
      <Button @click="setSampleContent" variant="primary">
        Load Sample Content
      </Button>
    </div>
    
    <!-- Main Editor -->
    <div class="mb-8">
      <TextEditor
        v-model="content"
        label="Main Text Editor"
        placeholder="Start typing your content here..."
        :error="hasError ? 'This is a sample error message' : ''"
        :disabled="isDisabled"
        :required="true"
        min-height="400px"
        class="mb-4"
      />
      
      <div class="mt-4">
        <h3 class="text-lg font-semibold mb-2">HTML Output:</h3>
        <pre class="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-40 whitespace-pre-wrap">{{ content }}</pre>
      </div>
    </div>

    <!-- Secondary Editor -->
    <div class="mb-8">
      <TextEditor
        v-model="content2"
        label="Secondary Editor (Smaller)"
        placeholder="This is a secondary editor for testing..."
        min-height="200px"
        class="mb-4"
      />
      
      <div class="mt-4">
        <h3 class="text-lg font-semibold mb-2">HTML Output:</h3>
        <pre class="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-40 whitespace-pre-wrap">{{ content2 || '(empty)' }}</pre>
      </div>
    </div>

    <!-- Rendered Preview -->
    <div class="mb-8">
      <h3 class="text-lg font-semibold mb-4">Rendered Preview:</h3>
      <div class="p-6 border rounded-lg bg-white prose max-w-none" v-html="content"></div>
    </div>

    <!-- Features Documentation -->
    <div class="mt-12">
      <h2 class="text-2xl font-bold mb-6">Supported Features</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div class="bg-white p-6 rounded-lg border">
          <h3 class="text-lg font-semibold mb-3 text-blue-600">Text Formatting</h3>
          <ul class="list-disc list-inside space-y-1 text-sm">
            <li>Bold, Italic, Underline</li>
            <li>Strikethrough</li>
            <li>Subscript, Superscript</li>
            <li>Font family selection</li>
            <li>Text and highlight colors</li>
          </ul>
        </div>
        
        <div class="bg-white p-6 rounded-lg border">
          <h3 class="text-lg font-semibold mb-3 text-green-600">Structure</h3>
          <ul class="list-disc list-inside space-y-1 text-sm">
            <li>Headings (H1-H6)</li>
            <li>Paragraphs</li>
            <li>Bullet lists</li>
            <li>Numbered lists</li>
            <li>Blockquotes</li>
            <li>Code blocks</li>
          </ul>
        </div>
        
        <div class="bg-white p-6 rounded-lg border">
          <h3 class="text-lg font-semibold mb-3 text-purple-600">Advanced</h3>
          <ul class="list-disc list-inside space-y-1 text-sm">
            <li>Tables with headers</li>
            <li>Image insertion</li>
            <li>Link creation</li>
            <li>Text alignment</li>
            <li>Undo/Redo</li>
            <li>Responsive toolbar</li>
          </ul>
        </div>
        
        <div class="bg-white p-6 rounded-lg border">
          <h3 class="text-lg font-semibold mb-3 text-red-600">Integration</h3>
          <ul class="list-disc list-inside space-y-1 text-sm">
            <li>Vue 3 composition API</li>
            <li>v-model support</li>
            <li>Error state handling</li>
            <li>Disabled state</li>
            <li>Form validation ready</li>
            <li>TypeScript support</li>
          </ul>
        </div>
        
        <div class="bg-white p-6 rounded-lg border">
          <h3 class="text-lg font-semibold mb-3 text-orange-600">UI/UX</h3>
          <ul class="list-disc list-inside space-y-1 text-sm">
            <li>Intuitive toolbar</li>
            <li>Grouped controls</li>
            <li>Dropdown menus</li>
            <li>Color pickers</li>
            <li>Responsive design</li>
            <li>Tailwind CSS styling</li>
          </ul>
        </div>
        
        <div class="bg-white p-6 rounded-lg border">
          <h3 class="text-lg font-semibold mb-3 text-indigo-600">Customization</h3>
          <ul class="list-disc list-inside space-y-1 text-sm">
            <li>Custom height</li>
            <li>Placeholder text</li>
            <li>Label support</li>
            <li>Required field indicator</li>
            <li>Custom CSS classes</li>
            <li>Extensible architecture</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Usage Instructions -->
    <div class="mt-12 bg-gray-50 p-6 rounded-lg">
      <h2 class="text-xl font-bold mb-4">Usage Instructions</h2>
      <div class="space-y-4 text-sm">
        <div>
          <h4 class="font-semibold">Basic Usage:</h4>
          <pre class="bg-white p-3 rounded mt-2 overflow-x-auto"><code>&lt;TextEditor v-model="content" label="Content" /&gt;</code></pre>
        </div>
        
        <div>
          <h4 class="font-semibold">With Error Handling:</h4>
          <pre class="bg-white p-3 rounded mt-2 overflow-x-auto"><code>&lt;TextEditor 
  v-model="content" 
  label="Content" 
  :error="form.errors.content"
  required 
/&gt;</code></pre>
        </div>
        
        <div>
          <h4 class="font-semibold">Custom Height:</h4>
          <pre class="bg-white p-3 rounded mt-2 overflow-x-auto"><code>&lt;TextEditor 
  v-model="content" 
  label="Content" 
  min-height="500px"
  placeholder="Enter detailed content..."
/&gt;</code></pre>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.prose {
  max-width: none;
}

.prose h1 {
  @apply text-3xl font-bold mb-4;
}

.prose h2 {
  @apply text-2xl font-bold mb-3;
}

.prose h3 {
  @apply text-xl font-bold mb-2;
}

.prose p {
  @apply mb-3;
}

.prose ul {
  @apply list-disc list-inside mb-3;
}

.prose ol {
  @apply list-decimal list-inside mb-3;
}

.prose blockquote {
  @apply border-l-4 border-gray-300 pl-4 italic text-gray-600 mb-3;
}

.prose table {
  @apply border-collapse border border-gray-300 mb-3 w-full;
}

.prose th {
  @apply border border-gray-300 px-3 py-2 bg-gray-100 font-semibold;
}

.prose td {
  @apply border border-gray-300 px-3 py-2;
}

.prose code {
  @apply bg-gray-100 px-1 py-0.5 rounded text-sm font-mono;
}

.prose pre {
  @apply bg-gray-100 p-3 rounded mb-3 overflow-x-auto;
}

.prose a {
  @apply text-blue-600 underline;
}
</style>
