<script setup lang="ts">
import { ref } from 'vue';
import { Head, useForm } from '@inertiajs/vue3';
import TextEditor from '@/components/common/shared/TextEditor.vue';
import VInput from '@/components/common/shared/VInput.vue';
import VSelect from '@/components/common/shared/VSelect.vue';
import Button from '@/components/common/shared/Button.vue';
import { useValidation } from '@/composables/useValidation';

// Form data type
interface ArticleFormType {
  title: string;
  content: string;
  summary: string;
  category: string;
  status: string;
}

// Initialize form with validation
const { form, validateForm, validateBeforePreview, isPreview, setPreview } = useValidation<ArticleFormType>({
  title: '',
  content: '',
  summary: '',
  category: '',
  status: 'draft',
});

// Form options
const categoryOptions = [
  { label: 'Technology', value: 'technology' },
  { label: 'Business', value: 'business' },
  { label: 'Design', value: 'design' },
  { label: 'Marketing', value: 'marketing' },
];

const statusOptions = [
  { label: 'Draft', value: 'draft' },
  { label: 'Published', value: 'published' },
  { label: 'Archived', value: 'archived' },
];

// Validation rules
const validationRules = {
  title: ['required', 'min:3', 'max:255'],
  content: ['required', 'min:10'],
  summary: ['required', 'min:10', 'max:500'],
  category: ['required'],
  status: ['required'],
};

// Form handlers
const handlePreview = () => {
  if (validateBeforePreview(validationRules)) {
    // Preview is automatically set by validateBeforePreview
  }
};

const handleSubmit = () => {
  if (validateForm(validationRules)) {
    // Simulate form submission
    alert('Form submitted successfully!\n\nData:\n' + JSON.stringify(form.data(), null, 2));
    
    // Reset form
    form.reset();
    setPreview(false);
  }
};

const handleCancel = () => {
  setPreview(false);
};

// Sample content for testing
const loadSampleContent = () => {
  form.title = 'Getting Started with TipTap Rich Text Editor';
  form.content = `
    <h1>Introduction to TipTap</h1>
    <p>TipTap is a <strong>powerful</strong> and <em>flexible</em> rich text editor for Vue.js applications. It provides a clean API and extensive customization options.</p>
    
    <h2>Key Features</h2>
    <ul>
      <li>Modular architecture with extensions</li>
      <li>Framework agnostic core</li>
      <li>TypeScript support</li>
      <li>Collaborative editing capabilities</li>
    </ul>
    
    <h2>Getting Started</h2>
    <p>To get started with TipTap, you need to install the core package and any extensions you want to use:</p>
    
    <pre><code>npm install @tiptap/vue-3 @tiptap/starter-kit</code></pre>
    
    <blockquote>
      TipTap is designed to be extensible and customizable, making it perfect for any rich text editing needs.
    </blockquote>
    
    <h3>Basic Usage</h3>
    <p>Here's a simple example of how to use TipTap in your Vue component:</p>
    
    <table>
      <thead>
        <tr>
          <th>Extension</th>
          <th>Purpose</th>
          <th>Required</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>StarterKit</td>
          <td>Basic functionality</td>
          <td>Yes</td>
        </tr>
        <tr>
          <td>TextStyle</td>
          <td>Text styling</td>
          <td>No</td>
        </tr>
        <tr>
          <td>Color</td>
          <td>Text colors</td>
          <td>No</td>
        </tr>
      </tbody>
    </table>
    
    <p>For more information, visit the <a href="https://tiptap.dev">official documentation</a>.</p>
  `;
  form.summary = 'A comprehensive guide to getting started with TipTap rich text editor in Vue.js applications. Learn about its features, installation, and basic usage.';
  form.category = 'technology';
  form.status = 'draft';
};
</script>

<template>
  <Head title="TextEditor Form Demo" />
  
  <div class="p-8 max-w-4xl mx-auto">
    <h1 class="text-3xl font-bold mb-8">TextEditor Form Integration Demo</h1>
    
    <!-- Control buttons -->
    <div class="mb-6 space-x-4">
      <Button @click="loadSampleContent" variant="primary">
        Load Sample Content
      </Button>
      <Button @click="form.reset(); setPreview(false)" variant="outline">
        Clear Form
      </Button>
    </div>
    
    <!-- Form -->
    <form @submit.prevent="handleSubmit" class="space-y-6">
      <div class="bg-white p-6 rounded-lg border">
        <h2 class="text-xl font-semibold mb-6">Article Information</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Title -->
          <div class="md:col-span-2">
            <VInput
              v-model="form.title"
              :error="form.errors.title"
              label="Article Title"
              placeholder="Enter article title..."
              required
              :disabled="isPreview"
              class="w-full"
            />
          </div>
          
          <!-- Category -->
          <div>
            <VSelect
              v-model="form.category"
              :error="form.errors.category"
              :options="categoryOptions"
              label="Category"
              required
              :disabled="isPreview"
              class="w-full"
            />
          </div>
          
          <!-- Status -->
          <div>
            <VSelect
              v-model="form.status"
              :error="form.errors.status"
              :options="statusOptions"
              label="Status"
              required
              :disabled="isPreview"
              class="w-full"
            />
          </div>
          
          <!-- Summary -->
          <div class="md:col-span-2">
            <TextEditor
              v-model="form.summary"
              :error="form.errors.summary"
              label="Article Summary"
              placeholder="Enter a brief summary of the article..."
              required
              :disabled="isPreview"
              min-height="120px"
              class="w-full"
            />
          </div>
          
          <!-- Main Content -->
          <div class="md:col-span-2">
            <TextEditor
              v-model="form.content"
              :error="form.errors.content"
              label="Article Content"
              placeholder="Write your article content here..."
              required
              :disabled="isPreview"
              min-height="400px"
              class="w-full"
            />
          </div>
        </div>
      </div>
      
      <!-- Form Actions -->
      <div class="flex justify-center space-x-4">
        <Button 
          v-if="!isPreview"
          type="button" 
          @click="handlePreview"
          variant="outline"
        >
          Preview
        </Button>
        <Button 
          v-if="isPreview"
          type="button" 
          @click="handleCancel"
          variant="outline"
        >
          Edit
        </Button>
        <Button type="submit" variant="primary">
          {{ isPreview ? 'Confirm & Submit' : 'Submit' }}
        </Button>
      </div>
    </form>
    
    <!-- Preview Section -->
    <div v-if="isPreview" class="mt-8 bg-gray-50 p-6 rounded-lg">
      <h2 class="text-xl font-semibold mb-4">Preview</h2>
      <div class="bg-white p-6 rounded border">
        <h1 class="text-2xl font-bold mb-2">{{ form.title }}</h1>
        <div class="text-sm text-gray-600 mb-4">
          Category: {{ categoryOptions.find(c => c.value === form.category)?.label }} | 
          Status: {{ statusOptions.find(s => s.value === form.status)?.label }}
        </div>
        
        <div class="mb-6">
          <h3 class="text-lg font-semibold mb-2">Summary</h3>
          <div class="prose max-w-none" v-html="form.summary"></div>
        </div>
        
        <div>
          <h3 class="text-lg font-semibold mb-2">Content</h3>
          <div class="prose max-w-none" v-html="form.content"></div>
        </div>
      </div>
    </div>
    
    <!-- Form Data Display -->
    <div class="mt-8 bg-gray-100 p-6 rounded-lg">
      <h2 class="text-xl font-semibold mb-4">Form Data (JSON)</h2>
      <pre class="bg-white p-4 rounded text-sm overflow-auto max-h-60 whitespace-pre-wrap">{{ JSON.stringify(form.data(), null, 2) }}</pre>
    </div>
    
    <!-- Integration Guide -->
    <div class="mt-8 bg-blue-50 p-6 rounded-lg">
      <h2 class="text-xl font-semibold mb-4 text-blue-800">Integration Guide</h2>
      <div class="space-y-4 text-sm">
        <div>
          <h4 class="font-semibold text-blue-700">Replace VTextarea with TextEditor:</h4>
          <pre class="bg-white p-3 rounded mt-2 overflow-x-auto"><code>&lt;!-- Before --&gt;
&lt;VTextarea v-model="form.description" :error="form.errors.description" /&gt;

&lt;!-- After --&gt;
&lt;TextEditor v-model="form.description" :error="form.errors.description" /&gt;</code></pre>
        </div>
        
        <div>
          <h4 class="font-semibold text-blue-700">With Validation:</h4>
          <pre class="bg-white p-3 rounded mt-2 overflow-x-auto"><code>&lt;TextEditor
  v-model="form.content"
  :error="form.errors.content"
  label="Content"
  placeholder="Enter content..."
  required
  :disabled="isPreview"
  min-height="300px"
/&gt;</code></pre>
        </div>
        
        <div>
          <h4 class="font-semibold text-blue-700">Key Benefits:</h4>
          <ul class="list-disc list-inside mt-2 space-y-1">
            <li>Drop-in replacement for VTextarea</li>
            <li>Same v-model and error handling patterns</li>
            <li>Rich text formatting capabilities</li>
            <li>Consistent with existing form components</li>
            <li>Built-in validation support</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.prose {
  max-width: none;
}

.prose h1 {
  @apply text-2xl font-bold mb-4;
}

.prose h2 {
  @apply text-xl font-bold mb-3;
}

.prose h3 {
  @apply text-lg font-bold mb-2;
}

.prose p {
  @apply mb-3;
}

.prose ul {
  @apply list-disc list-inside mb-3;
}

.prose ol {
  @apply list-decimal list-inside mb-3;
}

.prose blockquote {
  @apply border-l-4 border-gray-300 pl-4 italic text-gray-600 mb-3;
}

.prose table {
  @apply border-collapse border border-gray-300 mb-3 w-full;
}

.prose th {
  @apply border border-gray-300 px-3 py-2 bg-gray-100 font-semibold;
}

.prose td {
  @apply border border-gray-300 px-3 py-2;
}

.prose code {
  @apply bg-gray-100 px-1 py-0.5 rounded text-sm font-mono;
}

.prose pre {
  @apply bg-gray-100 p-3 rounded mb-3 overflow-x-auto;
}

.prose a {
  @apply text-blue-600 underline;
}
</style>
