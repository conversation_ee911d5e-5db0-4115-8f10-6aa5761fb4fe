<?php

namespace Src\Domain\Admin\Controllers;

use Illuminate\Http\JsonResponse;
use Src\Domain\Admin\Requests\Editor\UploadRequest;
use Src\Domain\Admin\Services\EditorService;
use Src\Enums\ResultCode;

class EditorController extends Controller
{
    /**
     * Upload image temporarily for CKEditor
     *
     * @param UploadRequest $request
     * @param EditorService $service
     * @return JsonResponse
     */
    public function upload(UploadRequest $request, EditorService $service): JsonResponse
    {
        $result = $service->uploadImageTemporary($request->validatedForm());
        if (!$result) {
            return json_response(ResultCode::ERROR, null, __('flash.upload.failed'));
        }
        return json_response(ResultCode::SUCCESS, $result);
    }
}
