<?php

namespace Src\Domain\Admin\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Src\Domain\Admin\Forms\Editor\UploadForm;
use Src\Enums\FileDiv;
use Src\Traits\StorageFileTrait;
use Src\Traits\Utils\FileUpload\FileUploadable;

class EditorService
{
    use FileUploadable, StorageFileTrait;

    /**
     * Upload image temporarily for CKEditor
     *
     * @param UploadForm $form
     * @return array|null
     */
    public function uploadImageTemporary(UploadForm $form): ?array
    {
        try {
            $file = $form->getUpload();

            if (!$file instanceof UploadedFile) {
                return null;
            }

            // Upload to temporary storage
            $tempResult = $this->putFileToTmpStorage($file, 'editor/temp');

            if (!$tempResult) {
                return null;
            }

            return [
                'url' => $tempResult['filePath'],
                'uploaded' => true,
                'fileName' => $tempResult['originFileName'],
                'tempPath' => $tempResult['filePath']
            ];

        } catch (\Exception $e) {
            logger()->error('Failed to upload temporary image for CKEditor', [
                'error' => $e->getMessage(),
                'file' => $file->getClientOriginalName() ?? 'unknown'
            ]);

            return null;
        }
    }

    /**
     * Move images from temporary to permanent storage
     *
     * @param string $content HTML content containing temp image URLs
     * @return string Updated HTML content with permanent URLs
     */
    public function moveTemporaryImagesToPermanent(string $content): string
    {
        try {
            // Find all temporary image URLs in content
            preg_match_all('/\/storage\/editor\/temp\/[^"\'>\s]+/', $content, $matches);

            if (empty($matches[0])) {
                return $content;
            }

            $tempUrls = $matches[0];

            foreach ($tempUrls as $tempUrl) {
                // Extract file path from URL
                $tempPath = str_replace('/storage/', '', $tempUrl);

                if (Storage::disk('public')->exists($tempPath)) {
                    // Generate new permanent file name
                    $fileName = basename($tempPath);
                    $permanentPath = 'editor/images/' . $fileName;

                    // Move file to permanent location
                    Storage::disk('public')->move($tempPath, $permanentPath);

                    // Create storage file record
                    $fileInfo = [
                        'file_path' => $permanentPath,
                        'file_size' => Storage::disk('public')->size($permanentPath),
                        'file_type' => Storage::disk('public')->mimeType($permanentPath),
                        'file_url' => asset('storage/' . $permanentPath),
                        'file_div' => FileDiv::IMAGE
                    ];

                    $storageFile = $this->storageFileQuery()->createOrThrow($fileInfo);

                    // Replace temp URL with permanent URL in content
                    $permanentUrl = asset('storage/' . $permanentPath);
                    $content = str_replace($tempUrl, $permanentUrl, $content);

                    logger()->info('Moved temporary image to permanent storage', [
                        'temp_path' => $tempPath,
                        'permanent_path' => $permanentPath,
                        'storage_file_id' => $storageFile->id
                    ]);
                }
            }

            return $content;

        } catch (\Exception $e) {
            logger()->error('Failed to move temporary images to permanent storage', [
                'error' => $e->getMessage()
            ]);

            return $content;
        }
    }
}
