<?php

namespace Src\Domain\Admin\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Src\Domain\Admin\Forms\Editor\UploadForm;
use Src\Enums\FileDiv;
use Src\Traits\StorageFileTrait;

class EditorService
{
    use StorageFileTrait;

    /**
     * Upload image for CKEditor
     *
     * @param UploadForm $form
     * @return array|null
     */
    public function uploadImage(UploadForm $form): ?array
    {
        try {
            $file = $form->getUpload();

            if (!$file instanceof UploadedFile) {
                return null;
            }

            // Create storage file record
            $storageFileId = $this->createStorageFile(
                $file,
                FileDiv::IMAGE,
                'editor/images'
            );

            if (!$storageFileId) {
                return null;
            }

            // Get the file URL
            $fileUrl = $this->getFileUrl($storageFileId);

            return [
                'url' => $fileUrl,
                'uploaded' => true,
                'fileName' => $file->getClientOriginalName(),
                'storageFileId' => $storageFileId
            ];

        } catch (\Exception $e) {
            logger()->error('Failed to upload image for CKEditor', [
                'error' => $e->getMessage(),
                'file' => $file->getClientOriginalName() ?? 'unknown'
            ]);

            return null;
        }
    }

    /**
     * Get file URL from storage file ID
     *
     * @param int $storageFileId
     * @return string
     */
    private function getFileUrl(int $storageFileId): string
    {
        // You may need to adjust this based on your StorageFile model structure
        $storageFile = \App\Eloquent\StorageFile::find($storageFileId);

        if (!$storageFile) {
            return '';
        }

        $disk = config('filesystems.default');

        if ($disk === 's3') {
            return Storage::disk($disk)->url($storageFile->file_path);
        }

        return asset('storage/' . $storageFile->file_path);
    }
}
